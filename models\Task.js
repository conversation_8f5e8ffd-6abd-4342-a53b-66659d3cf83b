/**
 * 任务模型类
 * 处理任务相关的数据库操作
 */
class Task {
  constructor(taskData = {}) {
    this.id = taskData.id || null;
    this.title = taskData.title || '';
    this.description = taskData.description || '';
    this.category = taskData.category || 'other';
    this.difficulty = taskData.difficulty || 'medium';
    this.requirements = taskData.requirements || '';
    this.deadline = taskData.deadline || null;
    this.budget = taskData.budget || null;
    this.budget_status = taskData.budget_status || 'confirmed';
    this.commission_amount = taskData.commission_amount || null;
    this.commission_status = taskData.commission_status || 'confirmed';
    this.status = taskData.status || 'published';
    this.publisher_id = taskData.publisher_id || null;
    this.created_at = taskData.created_at || null;
    this.updated_at = taskData.updated_at || null;
  }

  /**
   * 根据ID查找任务
   * @param {number} id 任务ID
   * @returns {Promise<Task|null>} 任务对象或null
   */
  static async findById(id) {
    try {
      const [rows] = await global.db.execute(
        'SELECT * FROM tasks WHERE id = ?',
        [id]
      );

      if (rows.length === 0) {
        return null;
      }

      return new Task(rows[0]);
    } catch (error) {
      console.error('查找任务错误:', error);
      throw new Error('数据库查询失败');
    }
  }

  /**
   * 创建新任务
   * @param {Object} taskData 任务数据
   * @returns {Promise<Task>} 创建的任务对象
   */
  static async create(taskData) {
    try {
      // 验证必填字段
      if (!taskData.title || !taskData.publisher_id) {
        throw new Error('任务标题和发布者不能为空');
      }

      // 如果预算状态为已确定，则预算不能为空
      if (taskData.budget_status === 'confirmed' && (!taskData.budget || parseFloat(taskData.budget) <= 0)) {
        throw new Error('预算状态为已确定时，预算金额不能为空且必须大于0');
      }

      const [result] = await global.db.execute(
        `INSERT INTO tasks (title, description, category, difficulty, requirements,
         deadline, budget, budget_status, commission_amount, commission_status, publisher_id, status)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          taskData.title,
          taskData.description || '',
          taskData.category || 'other',
          taskData.difficulty || 'medium',
          taskData.requirements || '',
          taskData.deadline || null,
          taskData.budget || null,
          taskData.budget_status || 'confirmed',
          taskData.commission_amount || null,
          taskData.commission_status || 'confirmed',
          taskData.publisher_id,
          'published'
        ]
      );

      // 返回创建的任务
      return await Task.findById(result.insertId);
    } catch (error) {
      console.error('创建任务错误:', error);
      throw new Error('创建任务失败');
    }
  }

  /**
   * 更新任务信息
   * @returns {Promise<boolean>} 更新是否成功
   */
  async update() {
    try {
      const [result] = await global.db.execute(
        `UPDATE tasks SET title = ?, description = ?, category = ?, difficulty = ?,
         requirements = ?, deadline = ?, budget = ?, budget_status = ?,
         commission_amount = ?, commission_status = ?, status = ?,
         updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
        [
          this.title,
          this.description,
          this.category,
          this.difficulty,
          this.requirements,
          this.deadline,
          this.budget,
          this.budget_status,
          this.commission_amount,
          this.commission_status,
          this.status,
          this.id
        ]
      );

      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新任务错误:', error);
      throw new Error('更新任务失败');
    }
  }

  /**
   * 删除任务
   * @returns {Promise<boolean>} 删除是否成功
   */
  async delete() {
    try {
      const [result] = await global.db.execute(
        'DELETE FROM tasks WHERE id = ?',
        [this.id]
      );

      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除任务错误:', error);
      throw new Error('删除任务失败');
    }
  }

  /**
   * 获取所有任务
   * @param {Object} options 查询选项
   * @returns {Promise<Array>} 任务列表
   */
  static async findAll(options = {}) {
    try {
      let query = `
        SELECT t.*, u.username as publisher_name, u.real_name as publisher_real_name
        FROM tasks t 
        LEFT JOIN users u ON t.publisher_id = u.id
      `;
      let params = [];
      let conditions = [];

      // 添加分类过滤
      if (options.category) {
        conditions.push('t.category = ?');
        params.push(options.category);
      }

      // 添加状态过滤
      if (options.status) {
        conditions.push('t.status = ?');
        params.push(options.status);
      }

      // 添加难度过滤
      if (options.difficulty) {
        conditions.push('t.difficulty = ?');
        params.push(options.difficulty);
      }

      // 添加发布者过滤
      if (options.publisher_id) {
        conditions.push('t.publisher_id = ?');
        params.push(options.publisher_id);
      }

      // 添加搜索条件
      if (options.search) {
        conditions.push('(t.title LIKE ? OR t.description LIKE ?)');
        const searchTerm = `%${options.search}%`;
        params.push(searchTerm, searchTerm);
      }

      // 添加预算范围过滤
      if (options.min_budget) {
        conditions.push('t.budget >= ?');
        params.push(options.min_budget);
      }
      if (options.max_budget) {
        conditions.push('t.budget <= ?');
        params.push(options.max_budget);
      }

      // 排除已被接单的任务（已接单的任务转到订单管理）
      if (options.exclude_taken) {
        conditions.push(`NOT EXISTS (
          SELECT 1 FROM orders o WHERE o.task_id = t.id AND o.status IN ('pending_deposit', 'in_progress', 'pending_final', 'completed')
        )`);
      }

      // 添加写手权限过滤
      if (options.writer_filter) {
        const writerFilter = options.writer_filter;
        if (writerFilter.show_available && writerFilter.writer_id) {
          // 写手可以看到：1. 已发布且未被接单的任务 2. 自己接单的任务
          const writerCondition = `(
            (t.status = 'published' AND t.id NOT IN (
              SELECT DISTINCT task_id FROM orders WHERE status IN ('pending_deposit', 'in_progress', 'pending_final', 'completed')
            ))
            OR
            t.id IN (
              SELECT DISTINCT task_id FROM orders WHERE writer_id = ?
            )
          )`;
          conditions.push(writerCondition);
          params.push(writerFilter.writer_id);
        }
      }

      // 组合条件
      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }

      // 添加排序
      const sortBy = options.sort_by || 'created_at';
      const sortOrder = options.sort_order || 'DESC';
      query += ` ORDER BY t.${sortBy} ${sortOrder}`;

      // 添加分页
      if (options.limit) {
        const limit = parseInt(options.limit);
        if (!isNaN(limit) && limit > 0) {
          if (options.offset) {
            const offset = parseInt(options.offset);
            if (!isNaN(offset) && offset >= 0) {
              query += ` LIMIT ${offset}, ${limit}`;
            } else {
              query += ` LIMIT ${limit}`;
            }
          } else {
            query += ` LIMIT ${limit}`;
          }
        }
      }

      const [rows] = await global.db.execute(query, params);
      return rows.map(row => {
        const task = new Task(row);
        task.publisher_name = row.publisher_name;
        task.publisher_real_name = row.publisher_real_name;
        return task;
      });
    } catch (error) {
      console.error('查询任务列表错误:', error);
      throw new Error('查询任务列表失败');
    }
  }

  /**
   * 获取可接单的任务列表
   * @param {Object} options 查询选项
   * @returns {Promise<Array>} 可接单任务列表
   */
  static async findAvailable(options = {}) {
    const availableOptions = {
      ...options,
      status: 'published'
    };
    return await Task.findAll(availableOptions);
  }

  /**
   * 根据分类获取任务统计
   * @returns {Promise<Array>} 分类统计数据
   */
  static async getCategoryStats() {
    try {
      const [rows] = await global.db.execute(`
        SELECT 
          category,
          COUNT(*) as total_count,
          COUNT(CASE WHEN status = 'published' THEN 1 END) as available_count,
          COUNT(CASE WHEN status = 'taken' THEN 1 END) as taken_count,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
          AVG(budget) as avg_budget
        FROM tasks 
        GROUP BY category
        ORDER BY total_count DESC
      `);

      return rows;
    } catch (error) {
      console.error('获取分类统计错误:', error);
      throw new Error('获取分类统计失败');
    }
  }

  /**
   * 搜索任务
   * @param {string} keyword 搜索关键词
   * @param {Object} options 搜索选项
   * @returns {Promise<Array>} 搜索结果
   */
  static async search(keyword, options = {}) {
    const searchOptions = {
      ...options,
      search: keyword
    };
    return await Task.findAll(searchOptions);
  }

  /**
   * 更新任务状态
   * @param {string} status 新状态
   * @returns {Promise<boolean>} 更新是否成功
   */
  async updateStatus(status) {
    try {
      const validStatuses = ['published', 'taken', 'completed', 'cancelled'];
      if (!validStatuses.includes(status)) {
        throw new Error('无效的任务状态');
      }

      this.status = status;
      const [result] = await global.db.execute(
        'UPDATE tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [status, this.id]
      );

      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新任务状态错误:', error);
      throw new Error('更新任务状态失败');
    }
  }

  /**
   * 检查任务是否可以接单
   * @returns {boolean} 是否可以接单
   */
  canBeTaken() {
    return this.status === 'published';
  }

  /**
   * 检查任务是否已过期
   * @returns {boolean} 是否已过期
   */
  isExpired() {
    if (!this.deadline) {
      return false;
    }
    return new Date() > new Date(this.deadline);
  }

  /**
   * 获取任务的格式化信息
   * @returns {Object} 格式化的任务信息
   */
  getFormattedInfo() {
    const difficultyNames = {
      'easy': '简单',
      'medium': '中等',
      'hard': '困难'
    };

    const statusNames = {
      'published': '已发布',
      'taken': '已接单',
      'completed': '已完成',
      'cancelled': '已取消'
    };

    // 数据库ENUM值到中文名称的映射
    const categoryNames = {
      'javaweb': 'JavaWeb开发',
      'springboot': 'SpringBoot项目',
      'gui': 'GUI应用',
      'centos': 'CentOS运维',
      'document': '文档编写',
      'other': '其他'
    };

    return {
      ...this,
      category_name: categoryNames[this.category] || this.category,
      difficulty_name: difficultyNames[this.difficulty] || this.difficulty,
      status_name: statusNames[this.status] || this.status,
      is_expired: this.isExpired(),
      can_be_taken: this.canBeTaken()
    };
  }

  /**
   * 获取任务总数
   * @param {Object} conditions 查询条件
   * @returns {Promise<number>} 任务总数
   */
  static async count(conditions = {}) {
    try {
      let query = 'SELECT COUNT(*) as total FROM tasks';
      let params = [];
      let whereConditions = [];

      // 添加分类过滤
      if (conditions.category) {
        whereConditions.push('category = ?');
        params.push(conditions.category);
      }

      // 添加状态过滤
      if (conditions.status) {
        whereConditions.push('status = ?');
        params.push(conditions.status);
      }

      // 添加难度过滤
      if (conditions.difficulty) {
        whereConditions.push('difficulty = ?');
        params.push(conditions.difficulty);
      }

      // 添加发布者过滤
      if (conditions.publisher_id) {
        whereConditions.push('publisher_id = ?');
        params.push(conditions.publisher_id);
      }

      // 添加搜索条件
      if (conditions.search) {
        whereConditions.push('(title LIKE ? OR description LIKE ?)');
        const searchTerm = `%${conditions.search}%`;
        params.push(searchTerm, searchTerm);
      }

      // 添加预算范围过滤
      if (conditions.min_budget) {
        whereConditions.push('budget >= ?');
        params.push(conditions.min_budget);
      }
      if (conditions.max_budget) {
        whereConditions.push('budget <= ?');
        params.push(conditions.max_budget);
      }

      // 排除已被接单的任务（已接单的任务转到订单管理）
      if (conditions.exclude_taken) {
        whereConditions.push(`NOT EXISTS (
          SELECT 1 FROM orders o WHERE o.task_id = id AND o.status IN ('pending_deposit', 'in_progress', 'pending_final', 'completed')
        )`);
      }

      // 添加写手权限过滤
      if (conditions.writer_filter) {
        const writerFilter = conditions.writer_filter;
        if (writerFilter.show_available && writerFilter.writer_id) {
          // 写手可以看到：1. 已发布且未被接单的任务 2. 自己接单的任务
          const writerCondition = `(
            (status = 'published' AND id NOT IN (
              SELECT DISTINCT task_id FROM orders WHERE status IN ('pending_deposit', 'in_progress', 'pending_final', 'completed')
            ))
            OR
            id IN (
              SELECT DISTINCT task_id FROM orders WHERE writer_id = ?
            )
          )`;
          whereConditions.push(writerCondition);
          params.push(writerFilter.writer_id);
        }
      }

      if (whereConditions.length > 0) {
        query += ' WHERE ' + whereConditions.join(' AND ');
      }

      const [rows] = await global.db.execute(query, params);
      return rows[0].total;
    } catch (error) {
      console.error('获取任务总数错误:', error);
      throw new Error('获取任务总数失败');
    }
  }
}

module.exports = Task;